import { Icon } from "@/components/icon";
import { useProducts } from "@/store/productStore";
import type { SortByOption, SortOrderOption } from "@/store/productStore";
import { Button } from "@/ui/button";
import { Card, CardContent, CardHeader } from "@/ui/card";
import { Input } from "@/ui/input";
import { getProcessedImageUrl, getProductImageUrl } from "@/utils/imageUtils";
import { useQueryClient } from "@tanstack/react-query";
import { Empty, Popconfirm, Select, Switch, Tag } from "antd";
import Table, { type ColumnsType } from "antd/es/table";
import type React from "react";
import { useState } from "react";
import type { Product } from "#/entity";
import ProductDetailModal from "./detail-modal";
import type { ProductDetailModalProps } from "./detail-modal";
import ProductExportModal from "./export-modal";
import type { ProductExportModalProps } from "./export-modal";
import ProductModal from "./product-modal";
import type { ProductModalProps } from "./product-modal";

export default function ProductPage() {
	const queryClient = useQueryClient();
	const {
		data: products,
		pagination,
		isLoading,
		isFetching,
		error,
		setPage,
		searchName,
		setSearchName,
		clearSearch,
		isRankingMode,
		setIsRankingMode,
		sortBy,
		setSortBy,
		sortOrder,
		setSortOrder,
	} = useProducts();

	const [searchInput, setSearchInput] = useState("");
	const [productDetailModalProps, setProductDetailModalProps] = useState<ProductDetailModalProps>({
		title: "",
		show: false,
		product: undefined,
		onOk: () => {
			setProductDetailModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setProductDetailModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const [productModalProps, setProductModalProps] = useState<ProductModalProps>({
		title: "新建产品",
		show: false,
		mode: "create",
		initialData: undefined,
		onOk: () => {
			setProductModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setProductModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const [exportModalProps, setExportModalProps] = useState<ProductExportModalProps>({
		title: "导出产品数据",
		show: false,
		searchName: searchName || undefined,
		isRankingMode,
		sortBy,
		sortOrder,
		onOk: () => {
			setExportModalProps((prev) => ({ ...prev, show: false }));
		},
		onCancel: () => {
			setExportModalProps((prev) => ({ ...prev, show: false }));
		},
	});

	const onCreateDetailMoal = (record: Product) => {
		console.log(record);
		setProductDetailModalProps((prev) => ({
			...prev,
			show: true,
			title: record.name,
			product: record,
		}));
	};

	const handleOpenCreateModal = () => {
		setProductModalProps((prev) => ({
			...prev,
			title: "新建产品",
			show: true,
			mode: "create",
			initialData: undefined,
		}));
	};

	const handleOpenEditModal = (product: Product) => {
		setProductModalProps((prev) => ({
			...prev,
			title: "编辑产品",
			show: true,
			mode: "edit",
			initialData: product,
		}));
	};

	const handleOpenExportModal = () => {
		setExportModalProps((prev) => ({
			...prev,
			show: true,
			searchName: searchName || undefined,
			isRankingMode,
			sortBy,
			sortOrder,
		}));
	};

	const handleRetry = () => {
		queryClient.invalidateQueries({ queryKey: ["products"] });
	};

	const handlePageChange = (page: number) => {
		setPage(page);
	};

	const handleSearch = () => {
		setSearchName(searchInput.trim());
	};

	const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchInput(e.target.value);
	};

	const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleSearch();
		}
	};

	const handleClearSearch = () => {
		setSearchInput("");
		clearSearch();
	};

	const sortByOptions = [
		{ value: "quality", label: "整体质量" },
		{ value: "protein", label: "蛋白质含量" },
		{ value: "fat", label: "脂肪含量" },
		{ value: "carbs", label: "碳水含量" },
		{ value: "calories", label: "卡路里" },
		{ value: "quality_ingredients", label: "优质成分数量" },
		{ value: "questionable_ingredients", label: "可疑成分数量" },
	];

	const sortOrderOptions = [
		{ value: "desc", label: "从高到低" },
		{ value: "asc", label: "从低到高" },
	];

	const handleModeChange = (checked: boolean) => {
		setIsRankingMode(checked);
	};

	const handleSortByChange = (value: SortByOption) => {
		setSortBy(value);
	};

	const handleSortOrderChange = (value: SortOrderOption) => {
		setSortOrder(value);
	};

	const columns: ColumnsType<Product> = [
		{
			title: "产品图片",
			dataIndex: "image_url",
			align: "center",
			width: 100,
			render: (url, record) => {
				// 优先使用pet-food app的规则：根据产品ID构建图片URL
				const productImageUrl = record._id ? getProductImageUrl(record._id) : "";
				// 如果没有产品ID，则使用原有的URL处理逻辑
				const fallbackUrl = url ? getProcessedImageUrl(url, "other") : "";
				const finalUrl = productImageUrl || fallbackUrl;

				return finalUrl ? (
					<img
						src={finalUrl}
						alt="Product"
						style={{ width: 60, height: 60, objectFit: "cover" }}
						onError={(e) => {
							// 如果pet-food规则的图片加载失败，尝试使用原始URL
							if (productImageUrl && url) {
								const target = e.target as HTMLImageElement;
								target.src = getProcessedImageUrl(url, "other");
							}
						}}
					/>
				) : (
					<span className="text-gray-400">无图片</span>
				);
			},
		},
		{
			title: "产品名称",
			dataIndex: "name",
			align: "center",
			width: 200,
			render: (name) => name || "无名称",
		},
		{
			title: "品牌",
			dataIndex: "brand",
			align: "center",
			width: 120,
			render: (brand) => brand || "未知品牌",
		},
		{
			title: "优质成分",
			dataIndex: "quality_ingredients",
			align: "center",
			width: 180,
			render: (ingredients) =>
				ingredients && ingredients.length > 0 ? (
					<div className="flex flex-wrap gap-1 justify-center">
						{ingredients.slice(0, 2).map((ingredient: string) => (
							<Tag key={`quality-${ingredient}`} color="green">
								{ingredient}
							</Tag>
						))}
						{ingredients.length > 2 && <Tag>+{ingredients.length - 2}</Tag>}
					</div>
				) : (
					<span className="text-text-secondary">无</span>
				),
		},
		{
			title: "可疑成分",
			dataIndex: "questionable_ingredients",
			align: "center",
			width: 180,
			render: (ingredients) =>
				ingredients && ingredients.length > 0 ? (
					<div className="flex flex-wrap gap-1 justify-center">
						{ingredients.slice(0, 2).map((ingredient: string) => (
							<Tag key={`questionable-${ingredient}`} color="orange">
								{ingredient}
							</Tag>
						))}
						{ingredients.length > 2 && <Tag>+{ingredients.length - 2}</Tag>}
					</div>
				) : (
					<span className="text-text-secondary">无</span>
				),
		},
		{
			title: "过敏源",
			dataIndex: "allergen_ingredients",
			align: "center",
			width: 180,
			render: (ingredients) =>
				ingredients && ingredients.length > 0 ? (
					<div className="flex flex-wrap gap-1 justify-center">
						{ingredients.slice(0, 2).map((ingredient: string) => (
							<Tag key={`allergen-${ingredient}`} color="red">
								{ingredient}
							</Tag>
						))}
						{ingredients.length > 2 && <Tag>+{ingredients.length - 2}</Tag>}
					</div>
				) : (
					<span className="text-text-secondary">无</span>
				),
		},
		{
			title: "操作",
			key: "operation",
			align: "center",
			width: 100,
			render: (_, record) => (
				<div className="flex w-full justify-center text-gray-500">
					<Button variant="ghost" size="icon" onClick={() => onCreateDetailMoal(record)}>
						<Icon icon="mdi:card-account-details" size={18} />
					</Button>
					<Button variant="ghost" size="icon" onClick={() => handleOpenEditModal(record)}>
						<Icon icon="solar:pen-bold-duotone" size={18} />
					</Button>
					<Popconfirm title="删除该产品?" okText="是" cancelText="否" placement="left">
						<Button variant="ghost" size="icon">
							<Icon icon="mingcute:delete-2-fill" size={18} className="text-error!" />
						</Button>
					</Popconfirm>
				</div>
			),
		},
	];

	const getPageSize = () => {
		// 使用固定的页面大小，与后端保持一致
		return 10;
	};

	// 移除了成分翻译相关的调试代码，因为数据库已经是中文了

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<div>产品列表 {pagination && `(共 ${pagination.totalCount} 条)`}</div>
					<div className="flex gap-2">
						<Button onClick={handleOpenExportModal} variant="outline" className="flex items-center gap-2">
							<Icon icon="material-symbols:download" size={16} />
							导出Excel
						</Button>
						<Button onClick={handleOpenCreateModal}>新建</Button>
					</div>
				</div>
			</CardHeader>
			<CardContent>
				<div className="mb-4 flex flex-wrap gap-2 items-center">
					<div className="flex items-center mr-4">
						<span className="mr-2">高级检索:</span>
						<Switch checked={isRankingMode} onChange={handleModeChange} size="small" />
					</div>

					<div className="relative flex-1 max-w-sm">
						<Input
							placeholder="搜索产品名称"
							value={searchInput}
							onChange={handleSearchInputChange}
							onKeyPress={handleKeyPress}
							className="pr-10"
						/>
						{searchInput && (
							<button
								type="button"
								className="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
								onClick={handleClearSearch}
							>
								<Icon icon="mdi:close" size={18} />
							</button>
						)}
						<Button variant="ghost" size="icon" className="absolute right-0 top-0 h-full" onClick={handleSearch}>
							<Icon icon="mdi:magnify" size={18} />
						</Button>
					</div>

					{isRankingMode && (
						<>
							<Select value={sortBy} onChange={handleSortByChange} style={{ width: 140 }} options={sortByOptions} />
							<Select
								value={sortOrder}
								onChange={handleSortOrderChange}
								style={{ width: 120 }}
								options={sortOrderOptions}
							/>
						</>
					)}
				</div>

				{searchName && (
					<div className="mb-4 flex items-center gap-2 text-sm">
						<span>搜索结果："{searchName}"</span>
						<Button variant="outline" size="sm" onClick={handleClearSearch} className="h-7 px-2 py-0 text-xs">
							清除搜索
						</Button>
					</div>
				)}

				{isRankingMode && (
					<div className="mb-4 flex items-center gap-2 text-sm">
						<span>
							正在按照{sortByOptions.find((opt) => opt.value === sortBy)?.label || "整体质量"}
							{sortOrder === "desc" ? "从高到低" : "从低到高"}排序
						</span>
					</div>
				)}

				{error ? (
					<div className="flex flex-col items-center py-8 text-center">
						<Icon icon="clarity:error-standard-line" size={48} className="text-error mb-4" />
						<div className="text-error font-medium text-lg mb-2">加载失败</div>
						<div className="text-text-secondary mb-6">请求出错，请稍候重试</div>
						<Button onClick={handleRetry} variant="outline" className="flex items-center gap-2">
							<Icon icon="mdi:reload" size={16} />
							重新加载
						</Button>
					</div>
				) : (
					<div className="min-h-[500px]">
						<Table
							rowKey="_id"
							size="small"
							scroll={{ x: "max-content", y: 800 }}
							pagination={
								pagination
									? {
											total: pagination.totalCount,
											current: pagination.currentPage,
											pageSize: getPageSize(),
											showSizeChanger: false,
											onChange: handlePageChange,
											position: ["bottomCenter"],
										}
									: false
							}
							columns={columns}
							dataSource={(products || []).map((product: Product) => ({
								...product,
								_id: product._id || `temp-${Math.random()}`,
							}))}
							loading={isLoading || isFetching}
							locale={{
								emptyText: (
									<Empty
										image={Empty.PRESENTED_IMAGE_SIMPLE}
										description={searchName ? "没有找到匹配的产品" : "暂无数据"}
									/>
								),
							}}
						/>
					</div>
				)}
			</CardContent>
			<ProductDetailModal {...productDetailModalProps} />
			<ProductModal {...productModalProps} />
			<ProductExportModal {...exportModalProps} />
		</Card>
	);
}
