import { Icon } from "@/components/icon";
import { getProcessedImageUrl, getProductImageUrl } from "@/utils/imageUtils";
import { Descriptions, Divider, Empty, Image, Modal, Table, Tabs, Tag, Typography } from "antd";
import type { Product } from "#/entity";

export type ProductDetailModalProps = {
	title: string;
	show: boolean;
	onOk: VoidFunction;
	onCancel: VoidFunction;
	product?: Product;
};

const { Title, Text } = Typography;
const { TabPane } = Tabs;

export default function ProductDetailModal({ title, show, onOk, onCancel, product }: ProductDetailModalProps) {
	if (!product) {
		return (
			<Modal forceRender title={title} open={show} onOk={onOk} onCancel={onCancel} width={700}>
				<Empty description="暂无产品数据" />
			</Modal>
		);
	}

	const formatDate = (dateString: string) => {
		try {
			return new Date(dateString).toLocaleString("zh-CN", {
				year: "numeric",
				month: "2-digit",
				day: "2-digit",
				hour: "2-digit",
				minute: "2-digit",
			});
		} catch (e) {
			return "无效日期";
		}
	};

	const transformAnalysisToTableData = (analysisData: Record<string, any>) => {
		if (!analysisData) return [];

		return Object.entries(analysisData).map(([key, value]) => ({
			key,
			nutrient: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " "),
			value: typeof value === "string" ? value : `${value}%`,
		}));
	};

	const analysisColumns = [
		{
			title: "营养成分",
			dataIndex: "nutrient",
			key: "nutrient",
		},
		{
			title: "含量",
			dataIndex: "value",
			key: "value",
		},
	];

	// 获取产品图片URL，优先使用pet-food app规则
	const getProductImageUrlForModal = (product: Product) => {
		// 优先使用pet-food app的规则：根据产品ID构建图片URL
		const productImageUrl = product._id ? getProductImageUrl(product._id) : "";
		// 如果没有产品ID，则使用原有的URL处理逻辑
		const fallbackUrl = product.image_url ? getProcessedImageUrl(product.image_url, "other") : "";
		return productImageUrl || fallbackUrl;
	};

	return (
		<Modal
			forceRender
			title={<span className="text-lg font-medium">{title}</span>}
			open={show}
			onOk={onOk}
			onCancel={onCancel}
			width={700}
			footer={null}
		>
			<div className="flex flex-col space-y-6 py-2">
				{/* Product Image and Basic Info */}
				<div className="flex flex-col md:flex-row gap-6">
					<div className="flex justify-center items-start w-full md:w-1/3">
						{getProductImageUrlForModal(product) ? (
							<Image
								src={getProductImageUrlForModal(product)}
								alt={product.name}
								className="rounded-md object-cover"
								style={{ maxHeight: "200px", maxWidth: "100%" }}
								onError={(e) => {
									// 如果pet-food规则的图片加载失败，尝试使用原始URL
									if (product._id && product.image_url) {
										const target = e.target as HTMLImageElement;
										target.src = getProcessedImageUrl(product.image_url, "other");
									}
								}}
								fallback="data:image/png;base64,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"
							/>
						) : (
							<div className="flex items-center justify-center w-full h-[200px] bg-gray-100 rounded-md">
								<Icon icon="mdi:image-off" size={48} className="text-gray-400" />
							</div>
						)}
					</div>
					<div className="flex flex-col gap-2 w-full md:w-2/3">
						<Descriptions layout="vertical" column={{ xs: 1, sm: 2 }} size="small">
							<Descriptions.Item label="产品名称" span={2}>
								<Text strong>{product.name || "未知"}</Text>
							</Descriptions.Item>
							<Descriptions.Item label="品牌">{product.brand || "未知品牌"}</Descriptions.Item>
							<Descriptions.Item label="产品类型">{product.product_type || "未分类"}</Descriptions.Item>
							<Descriptions.Item label="估计热量">{product.est_calories || "未提供"}</Descriptions.Item>
							{product.product_url && (
								<Descriptions.Item label="产品链接" span={2}>
									<a
										href={product.product_url}
										target="_blank"
										rel="noopener noreferrer"
										className="text-blue-500 hover:underline truncate block"
									>
										{product.product_url}
									</a>
								</Descriptions.Item>
							)}
						</Descriptions>
					</div>
				</div>

				<Divider className="my-2" />

				{/* Ingredients */}
				<div>
					<Title level={5}>成分分析</Title>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
						<div className="flex flex-col">
							<Text strong className="mb-2 text-green-600">
								优质成分
							</Text>
							{product.quality_ingredients && product.quality_ingredients.length > 0 ? (
								<div className="flex flex-wrap gap-1">
									{product.quality_ingredients.map((ingredient) => (
										<Tag key={`quality-${ingredient}`} color="success">
											{ingredient}
										</Tag>
									))}
								</div>
							) : (
								<Text type="secondary">无优质成分</Text>
							)}
						</div>
						<div className="flex flex-col">
							<Text strong className="mb-2 text-orange-500">
								可疑成分
							</Text>
							{product.questionable_ingredients && product.questionable_ingredients.length > 0 ? (
								<div className="flex flex-wrap gap-1">
									{product.questionable_ingredients.map((ingredient) => (
										<Tag key={`questionable-${ingredient}`} color="warning">
											{ingredient}
										</Tag>
									))}
								</div>
							) : (
								<Text type="secondary">无可疑成分</Text>
							)}
						</div>
						<div className="flex flex-col">
							<Text strong className="mb-2 text-red-500">
								过敏源
							</Text>
							{product.allergen_ingredients && product.allergen_ingredients.length > 0 ? (
								<div className="flex flex-wrap gap-1">
									{product.allergen_ingredients.map((ingredient) => (
										<Tag key={`allergen-${ingredient}`} color="error">
											{ingredient}
										</Tag>
									))}
								</div>
							) : (
								<Text type="secondary">无过敏源</Text>
							)}
						</div>
					</div>
				</div>

				<Divider className="my-2" />

				{/* Analysis Data */}
				<div>
					<Tabs defaultActiveKey="guaranteed" className="mb-4">
						<Tabs.TabPane tab="成分保证分析" key="guaranteed">
							{product.guaranteed_analysis && Object.keys(product.guaranteed_analysis).length > 0 ? (
								<Table
									dataSource={transformAnalysisToTableData(product.guaranteed_analysis)}
									columns={analysisColumns}
									pagination={false}
									size="small"
									rowKey="nutrient"
								/>
							) : (
								<Empty description="无成分保证分析数据" />
							)}
						</Tabs.TabPane>
						<Tabs.TabPane tab="干物质分析" key="dry_matter">
							{product.dry_matter_analysis && Object.keys(product.dry_matter_analysis).length > 0 ? (
								<Table
									dataSource={transformAnalysisToTableData(product.dry_matter_analysis)}
									columns={analysisColumns}
									pagination={false}
									size="small"
									rowKey="nutrient"
								/>
							) : (
								<Empty description="无干物质分析数据" />
							)}
						</Tabs.TabPane>
					</Tabs>
				</div>

				{/* Timestamps */}
				<div className="flex justify-between text-xs text-gray-500 mt-4">
					<div>创建时间: {formatDate(product.created_at)}</div>
					<div>更新时间: {formatDate(product.updated_at)}</div>
				</div>
			</div>
		</Modal>
	);
}
