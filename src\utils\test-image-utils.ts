/**
 * 测试图像工具函数
 */

import { getBrandLogoUrl, getProcessedImageUrl, getProductImageUrl } from "./imageUtils";

// 测试函数
export const testImageUtils = () => {
	console.log("=== 图像工具函数测试 ===");

	// 测试产品图片URL生成
	const productId = "68209a1d4633d0e6f3800f4b";
	const productImageUrl = getProductImageUrl(productId);
	console.log(`产品图片URL: ${productImageUrl}`);
	console.log(`期望格式: http://localhost:3000/assets/product_iamges/68209a1d4633d0e6f3800f4b.png`);

	// 测试品牌Logo URL生成
	const brandId = "68209a1d4633d0e6f3800f4b";
	const brandLogoUrl = getBrandLogoUrl(brandId);
	console.log(`品牌Logo URL: ${brandLogoUrl}`);
	console.log(`期望格式: http://localhost:3000/assets/brand_logo/68209a1d4633d0e6f3800f4b.png`);

	// 测试catfooddb.com URL处理
	const catfoodUrl = "https://catfooddb.com/img/test.jpg";
	const processedCatfoodUrl = getProcessedImageUrl(catfoodUrl);
	console.log(`catfooddb.com URL处理: ${catfoodUrl} -> ${processedCatfoodUrl}`);

	// 测试相对路径URL处理
	const relativeUrl = "img/test.jpg";
	const processedRelativeUrl = getProcessedImageUrl(relativeUrl);
	console.log(`相对路径URL处理: ${relativeUrl} -> ${processedRelativeUrl}`);

	console.log("=== 测试完成 ===");
};

// 如果在开发环境，自动运行测试
if (import.meta.env.DEV) {
	testImageUtils();
}
