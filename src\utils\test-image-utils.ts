/**
 * 测试图像工具函数
 */

import { getProductImageUrl, getBrandLogoUrl, getProcessedImageUrl } from './imageUtils';

// 测试函数
export const testImageUtils = () => {
  console.log('=== 图像工具函数测试 ===');
  
  // 测试产品图片URL生成
  const productId = 'test-product-123';
  const productImageUrl = getProductImageUrl(productId);
  console.log(`产品图片URL: ${productImageUrl}`);
  
  // 测试品牌Logo URL生成
  const brandId = 'test-brand-456';
  const brandLogoUrl = getBrandLogoUrl(brandId);
  console.log(`品牌Logo URL: ${brandLogoUrl}`);
  
  // 测试catfooddb.com URL处理
  const catfoodUrl = 'https://catfooddb.com/img/test.jpg';
  const processedCatfoodUrl = getProcessedImageUrl(catfoodUrl);
  console.log(`catfooddb.com URL处理: ${catfoodUrl} -> ${processedCatfoodUrl}`);
  
  // 测试相对路径URL处理
  const relativeUrl = 'img/test.jpg';
  const processedRelativeUrl = getProcessedImageUrl(relativeUrl);
  console.log(`相对路径URL处理: ${relativeUrl} -> ${processedRelativeUrl}`);
  
  console.log('=== 测试完成 ===');
};

// 如果在开发环境，自动运行测试
if (import.meta.env.DEV) {
  testImageUtils();
}
