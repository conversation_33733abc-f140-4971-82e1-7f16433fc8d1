/**
 * 后端管理界面图片URL工具函数
 */

// 从环境变量获取API基础URL
const API_BASE_URL = import.meta.env.VITE_APP_BASE_API || "/api";

/**
 * 从API返回的相对路径构建完整URL
 * @param relativePath API返回的相对路径
 * @returns 完整的图片URL
 */
export const buildImageUrl = (relativePath: string): string => {
	if (!relativePath) return "";
	if (relativePath.startsWith("http")) return relativePath;

	// 确保路径不以斜杠开头，避免双斜杠
	const cleanPath = relativePath.startsWith("/") ? relativePath.slice(1) : relativePath;
	const fullUrl = `${API_BASE_URL}/${cleanPath}`;

	return fullUrl;
};

/**
 * 检查是否为有效的图片URL
 * @param url 图片URL
 * @returns 是否为有效URL
 */
export const isValidImageUrl = (url: string): boolean => {
	if (!url) return false;
	return url.startsWith(API_BASE_URL) || url.startsWith("http");
};

/**
 * 获取产品图片URL（遵循pet-food app规则）
 * @param productId 产品ID
 * @param extension 图片扩展名，默认为png
 * @returns 完整的图片URL
 */
export const getProductImageUrl = (productId: string, extension = "png"): string => {
	if (!productId) return "";
	return `${API_BASE_URL}/assets/product_iamges/${productId}.${extension}`;
};

/**
 * 获取品牌logo URL（遵循pet-food app规则）
 * @param brandId 品牌ID
 * @param extension 图片扩展名，默认为png
 * @returns 完整的图片URL
 */
export const getBrandLogoUrl = (brandId: string, extension = "png"): string => {
	if (!brandId) return "";
	return `${API_BASE_URL}/assets/brand_logo/${brandId}.${extension}`;
};

/**
 * 处理图像URL的统一函数，支持多种URL类型
 * @param url 原始URL
 * @param type 图像类型：'product' | 'brand' | 'other'
 * @param id 产品或品牌ID（当type为product或brand时使用）
 * @returns 处理后的图片URL
 */
export const getProcessedImageUrl = (
	url: string,
	type: "product" | "brand" | "other" = "other",
	id?: string,
): string => {
	if (!url) return "";

	// 如果是完整的HTTP URL，进行代理处理
	if (url.startsWith("http")) {
		// Handle catfooddb.com URLs
		if (url.includes("catfooddb.com")) {
			return url.replace("https://catfooddb.com", "/catfood-proxy");
		}
		return url;
	}

	// 如果是相对路径且以img/开头，通过catfooddb代理
	if (url.startsWith("img/") || url.startsWith("/img/")) {
		const cleanUrl = url.startsWith("/") ? url.slice(1) : url;
		return `/catfood-proxy/${cleanUrl}`;
	}

	// 如果提供了ID和类型，使用pet-food app的规则构建URL
	if (id && type === "product") {
		return getProductImageUrl(id);
	}

	if (id && type === "brand") {
		return getBrandLogoUrl(id);
	}

	// 其他情况，使用buildImageUrl处理
	return buildImageUrl(url);
};

/**
 * 获取图片基础URL
 */
export const getImageBaseUrl = (): string => {
	return API_BASE_URL;
};

/**
 * 批量构建图片URL
 * @param relativePaths 相对路径数组
 * @returns 完整URL数组
 */
export const buildImageUrls = (relativePaths: string[]): string[] => {
	return relativePaths.map((path) => buildImageUrl(path));
};
