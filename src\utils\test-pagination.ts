/**
 * 测试分页功能的工具函数
 */

import { productService } from "@/api/services/productService";
import { brandService } from "@/api/services/brandService";

export const testPagination = async () => {
  console.log('=== 分页功能测试 ===');
  
  try {
    // 测试产品分页
    console.log('测试产品API分页...');
    const productResponse = await productService.getProducts(1);
    console.log('产品API响应:', {
      totalCount: productResponse.totalCount,
      totalPages: productResponse.totalPages,
      currentPage: productResponse.currentPage,
      productsLength: productResponse.products?.length || 0
    });
    
    // 测试品牌分页
    console.log('测试品牌API分页...');
    const brandResponse = await brandService.getBrands(1);
    console.log('品牌API响应:', {
      totalCount: brandResponse.totalCount,
      totalPages: brandResponse.totalPages,
      currentPage: brandResponse.currentPage,
      brandsLength: brandResponse.brands?.length || 0
    });
    
    console.log('=== 分页测试完成 ===');
    
    return {
      products: productResponse,
      brands: brandResponse
    };
  } catch (error) {
    console.error('分页测试失败:', error);
    throw error;
  }
};

// 如果在开发环境，可以在控制台调用这个函数
if (import.meta.env.DEV) {
  // @ts-ignore
  window.testPagination = testPagination;
  console.log('分页测试函数已添加到 window.testPagination，可在控制台调用');
}
