/**
 * 成分显示工具函数
 * 由于数据库中的成分已经是中文，不再需要翻译功能
 */

/**
 * 直接返回成分名称（不进行翻译）
 * @param ingredient 成分名称
 * @returns 成分名称
 */
export const displayIngredient = (ingredient: string): string => {
  return ingredient || "";
};

/**
 * 批量处理成分列表（不进行翻译）
 * @param ingredients 成分名称列表
 * @returns 成分名称列表
 */
export const displayIngredients = (ingredients: string[]): string[] => {
  if (!ingredients || !Array.isArray(ingredients)) return [];
  return ingredients.filter(ingredient => ingredient && ingredient.trim() !== "");
};

/**
 * 格式化成分显示，移除空值和重复项
 * @param ingredients 成分名称列表
 * @returns 清理后的成分名称列表
 */
export const formatIngredients = (ingredients: string[]): string[] => {
  if (!ingredients || !Array.isArray(ingredients)) return [];
  
  // 移除空值、空字符串和重复项
  const uniqueIngredients = [...new Set(
    ingredients
      .filter(ingredient => ingredient && ingredient.trim() !== "")
      .map(ingredient => ingredient.trim())
  )];
  
  return uniqueIngredients;
};

/**
 * 获取成分显示文本，用于表格等场景
 * @param ingredients 成分列表
 * @param maxDisplay 最大显示数量，默认为2
 * @returns 格式化的显示文本
 */
export const getIngredientsDisplayText = (ingredients: string[], maxDisplay: number = 2): string => {
  const cleanIngredients = formatIngredients(ingredients);
  
  if (cleanIngredients.length === 0) return "无";
  
  if (cleanIngredients.length <= maxDisplay) {
    return cleanIngredients.join("、");
  }
  
  const displayItems = cleanIngredients.slice(0, maxDisplay);
  const remainingCount = cleanIngredients.length - maxDisplay;
  
  return `${displayItems.join("、")} 等${remainingCount}种`;
};
