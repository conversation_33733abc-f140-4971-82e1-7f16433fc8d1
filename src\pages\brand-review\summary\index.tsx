import type { BrandReviewSummary } from "@/api/services/brandReviewService";
import { Icon } from "@/components/icon";
import { useBrandReviewSummaryAdmin } from "@/store/brandReviewStore";
import { useUserInfo } from "@/store/userStore";
import { Button } from "@/ui/button";
import { Card, CardContent, CardHeader } from "@/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/ui/tooltip";
import { Rate } from "antd";
import Table, { type ColumnsType } from "antd/es/table";
import type { SortOrder } from "antd/es/table/interface";
import { useState } from "react";
import { useNavigate } from "react-router";
import { toast } from "sonner";
import { CreateBrandReviewModal } from "./create-brand-review-modal";

export default function BrandReviewSummaryManagement() {
	const { data, pagination, setPage, handleSortChange, sortBy, sortOrder, isLoading, createReview } =
		useBrandReviewSummaryAdmin();
	const navigate = useNavigate();
	const [createModalOpen, setCreateModalOpen] = useState(false);
	const userInfo = useUserInfo();

	const mapSortOrder = (
		field: string,
		currentSortBy: string,
		currentSortOrder: "asc" | "desc",
	): SortOrder | undefined => {
		if (field !== currentSortBy) return undefined;
		return currentSortOrder === "asc" ? "ascend" : "descend";
	};

	const columns: ColumnsType<BrandReviewSummary> = [
		{
			title: "品牌名称",
			dataIndex: "brand_name",
			ellipsis: true,
			width: 200,
		},
		{
			title: "评分",
			dataIndex: "average_rating",
			width: 200,
			sorter: true,
			sortOrder: mapSortOrder("average_rating", sortBy, sortOrder),
			render: (rating) => (
				<div className="flex items-center">
					<Rate allowHalf disabled defaultValue={rating / 2} className="text-sm" />
					<span className="ml-1">{rating.toFixed(1)}</span>
				</div>
			),
		},
		{
			title: "总评",
			dataIndex: "review_count",
			width: 60,
			sorter: true,
			sortOrder: mapSortOrder("review_count", sortBy, sortOrder),
		},
		{
			title: "短评",
			dataIndex: "short_review_count",
			width: 70,
		},
		{
			title: "长评",
			dataIndex: "long_review_count",
			width: 70,
		},
		{
			title: "操作",
			key: "operation",
			align: "center",
			width: 100,
			render: (_, record) => (
				<div className="flex w-full justify-center text-gray">
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button variant="ghost" size="icon" onClick={() => viewBrandReviews(record)}>
									<Icon icon="solar:chat-line-bold-duotone" size={18} />
								</Button>
							</TooltipTrigger>
							<TooltipContent>
								<p>查看评论</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>
			),
		},
	];

	const getPageSize = () => {
		// 使用固定的页面大小，与后端保持一致
		return 10;
	};

	const handlePageChange = (page: number) => {
		setPage(page);
	};

	const handleTableChange = (_: any, __: any, sorter: any) => {
		const newSortBy = sorter.field || "review_count";
		const newSortOrder = sorter.order ? (sorter.order === "ascend" ? "asc" : "desc") : "desc";
		handleSortChange(newSortBy, newSortOrder as "asc" | "desc");
	};

	const viewBrandReviews = (record: BrandReviewSummary) => {
		navigate("/brand-review/detail", {
			state: {
				brandId: record.brand_id,
				brandName: record.brand_name,
			},
		});
	};

	const handleCreateReview = (values: any) => {
		// Add current timestamp to review data and ensure user ID is set from current user
		const reviewData = {
			...values,
			user_id: values.user_id || userInfo.username || "",
			review_time: new Date().toISOString(),
		};

		createReview(reviewData, {
			onSuccess: () => {
				setCreateModalOpen(false);
				toast.success("评论创建成功");
			},
			onError: (error: any) => {
				toast.error("创建评论失败: " + (error.message || "未知错误"));
			},
		});
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<div>品牌评论管理</div>
					<Button onClick={() => setCreateModalOpen(true)}>
						<Icon icon="material-symbols:add" className="mr-2" size={10} />
						新增评论
					</Button>
				</div>
			</CardHeader>
			<CardContent>
				<Table
					rowKey="_id"
					size="small"
					scroll={{ x: "max-content" }}
					pagination={
						pagination && pagination.totalCount > 0
							? {
									total: pagination.totalCount,
									current: pagination.currentPage,
									pageSize: getPageSize(),
									showSizeChanger: false,
									onChange: handlePageChange,
									position: ["bottomCenter"],
								}
							: false
					}
					columns={columns}
					dataSource={data}
					loading={isLoading}
					onChange={handleTableChange}
				/>
			</CardContent>

			<CreateBrandReviewModal
				show={createModalOpen}
				onOk={handleCreateReview}
				onCancel={() => setCreateModalOpen(false)}
			/>
		</Card>
	);
}
