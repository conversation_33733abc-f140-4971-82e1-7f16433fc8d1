import brandService from "@/api/services/brandService";
import { Icon } from "@/components/icon";
import { useBrands } from "@/store/brandStore";
import { Button } from "@/ui/button";
import { Card, CardContent, CardHeader } from "@/ui/card";
import { Input } from "@/ui/input";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/ui/tooltip";
import { getBrandLogoUrl, getProcessedImageUrl } from "@/utils/imageUtils";
import { useQueryClient } from "@tanstack/react-query";
import { Empty, Popconfirm, message } from "antd";
import Table, { type ColumnsType } from "antd/es/table";
import { useState } from "react";
import type { Brand } from "#/entity";
import BrandModal from "./brand-modal";
import type { BrandModalProps } from "./brand-modal";

export default function BrandPage() {
	const queryClient = useQueryClient();

	const {
		data: brands,
		pagination,
		isLoading,
		isFetching,
		error,
		setPage,
		searchName,
		setSearchName,
		clearSearch,
	} = useBrands();

	const [brandModalProps, setBrandModalProps] = useState<BrandModalProps>({
		title: "新建品牌",
		show: false,
		mode: "create",
		initialData: undefined,
		onOk: () => {
			setBrandModalProps((prev) => ({ ...prev, show: false, initialData: undefined }));
		},
		onCancel: () => {
			setBrandModalProps((prev) => ({ ...prev, show: false, initialData: undefined }));
		},
	});

	const [searchInput, setSearchInput] = useState("");

	const handleRetry = () => {
		queryClient.invalidateQueries({ queryKey: ["brands"] });
	};

	const handlePageChange = (page: number) => {
		setPage(page);
	};

	const handleSearch = () => {
		setSearchName(searchInput.trim());
	};

	const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchInput(e.target.value);
	};

	const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			handleSearch();
		}
	};

	const handleClearSearch = () => {
		setSearchInput("");
		clearSearch();
	};

	const handleOpenCreateModal = () => {
		setBrandModalProps((prev) => ({
			...prev,
			title: "新建品牌",
			show: true,
			mode: "create",
			initialData: undefined,
		}));
	};

	const handleOpenEditModal = (brand: Brand) => {
		setBrandModalProps((prev) => ({
			...prev,
			title: "编辑品牌",
			show: true,
			mode: "edit",
			initialData: brand,
		}));
	};

	const handleDeleteBrand = async (id: string) => {
		try {
			const response = await brandService.deleteBrand(id);
			if (response?.success) {
				message.success("品牌删除成功");
				queryClient.invalidateQueries({ queryKey: ["brands"] });
			} else {
				message.error(response?.message || "删除失败，请重试");
			}
		} catch (error: any) {
			if (error.response?.data?.message) {
				message.error(error.response.data.message);
			} else if (error.message) {
				message.error(error.message);
			} else {
				message.error("删除失败，请重试");
			}
		}
	};

	// 获取品牌Logo URL，优先使用pet-food app规则
	const getBrandLogoUrlForTable = (brand: Brand) => {
		// 优先使用pet-food app的规则：根据品牌ID构建Logo URL
		const brandLogoUrl = brand._id ? getBrandLogoUrl(brand._id) : "";
		// 如果没有品牌ID，则使用原有的URL处理逻辑
		const fallbackUrl = brand.logo_url ? getProcessedImageUrl(brand.logo_url, "other") : "";
		return brandLogoUrl || fallbackUrl;
	};

	const columns: ColumnsType<Brand> = [
		{
			title: "品牌Logo",
			dataIndex: "logo_url",
			align: "center",
			width: 120,
			render: (url, record) => {
				const finalUrl = getBrandLogoUrlForTable(record);

				return finalUrl ? (
					<img
						src={finalUrl}
						alt="Brand Logo"
						style={{ width: 60, height: 60, objectFit: "cover" }}
						onError={(e) => {
							// 如果pet-food规则的Logo加载失败，尝试使用原始URL
							if (record._id && record.logo_url) {
								const target = e.target as HTMLImageElement;
								target.src = getProcessedImageUrl(record.logo_url, "other");
							} else {
								const target = e.target as HTMLImageElement;
								target.style.display = "none";
								const parent = target.parentElement;
								if (parent) {
									parent.innerHTML = '<span class="text-gray-400">图片加载失败</span>';
								}
							}
						}}
					/>
				) : (
					<span className="text-gray-400">无Logo</span>
				);
			},
		},
		{
			title: "品牌名称",
			dataIndex: "name",
			align: "center",
			width: 150,
			render: (name) => name || "无名称",
		},
		{
			title: "品牌描述",
			dataIndex: "desc",
			align: "left",
			width: 300,
			ellipsis: false,
			render: (desc) => {
				if (!desc) {
					return <span className="text-text-secondary">无描述</span>;
				}

				const truncatedDesc = desc.length > 100 ? `${desc.substring(0, 100)}...` : desc;

				return (
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<div className="cursor-help">
									<div className="line-clamp-2 text-sm leading-5">{truncatedDesc}</div>
								</div>
							</TooltipTrigger>
							<TooltipContent side="top" className="max-w-md">
								<div className="whitespace-pre-wrap text-sm max-h-40 overflow-y-auto">{desc}</div>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				);
			},
		},
		{
			title: "官网地址",
			dataIndex: "website_url",
			align: "center",
			width: 200,
			render: (url) =>
				url ? (
					<a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
						{url}
					</a>
				) : (
					<span className="text-text-secondary">未导入</span>
				),
		},
		{
			title: "创建时间",
			dataIndex: "created_at",
			align: "center",
			width: 180,
			render: (date) => {
				try {
					return date ? new Date(date).toLocaleString() : "无日期";
				} catch (e) {
					return "格式错误";
				}
			},
		},
		{
			title: "更新时间",
			dataIndex: "updated_at",
			align: "center",
			width: 180,
			render: (date) => {
				try {
					return date ? new Date(date).toLocaleString() : "无日期";
				} catch (e) {
					return "格式错误";
				}
			},
		},
		{
			title: "操作",
			key: "operation",
			align: "center",
			width: 100,
			render: (_, record) => (
				<div className="flex w-full justify-center text-gray-500">
					<Button variant="ghost" size="icon" onClick={() => handleOpenEditModal(record)}>
						<Icon icon="solar:pen-bold-duotone" size={18} />
					</Button>
					<Popconfirm
						title="删除该品牌?"
						okText="是"
						cancelText="否"
						placement="left"
						onConfirm={() => handleDeleteBrand(record._id)}
					>
						<Button variant="ghost" size="icon">
							<Icon icon="mingcute:delete-2-fill" size={18} className="text-error!" />
						</Button>
					</Popconfirm>
				</div>
			),
		},
	];

	const getPageSize = () => {
		if (!pagination || pagination.totalPages === 0) return 10;
		return Math.ceil(pagination.totalCount / pagination.totalPages);
	};

	return (
		<Card>
			<CardHeader>
				<div className="flex items-center justify-between">
					<div>品牌列表 {pagination && `(共 ${pagination.totalCount} 条)`}</div>
					<Button onClick={handleOpenCreateModal}>新建</Button>
				</div>
			</CardHeader>
			<CardContent>
				<div className="mb-4 flex gap-2">
					<div className="relative flex-1 max-w-sm">
						<Input
							placeholder="搜索品牌名称"
							value={searchInput}
							onChange={handleSearchInputChange}
							onKeyPress={handleKeyPress}
							className="pr-10"
						/>
						{searchInput && (
							<button
								type="button"
								className="absolute right-10 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
								onClick={handleClearSearch}
							>
								<Icon icon="mdi:close" size={18} />
							</button>
						)}
						<Button variant="ghost" size="icon" className="absolute right-0 top-0 h-full" onClick={handleSearch}>
							<Icon icon="mdi:magnify" size={18} />
						</Button>
					</div>
				</div>

				{searchName && (
					<div className="mb-4 flex items-center gap-2 text-sm">
						<span>搜索结果："{searchName}"</span>
						<Button variant="outline" size="sm" onClick={handleClearSearch} className="h-7 px-2 py-0 text-xs">
							清除搜索
						</Button>
					</div>
				)}

				{error ? (
					<div className="flex flex-col items-center py-8 text-center">
						<Icon icon="clarity:error-standard-line" size={48} className="text-error mb-4" />
						<div className="text-error font-medium text-lg mb-2">加载失败</div>
						<div className="text-text-secondary mb-6">请求出错，请稍候重试</div>
						<Button onClick={handleRetry} variant="outline" className="flex items-center gap-2">
							<Icon icon="mdi:reload" size={16} />
							重新加载
						</Button>
					</div>
				) : (
					<div className="min-h-[500px]">
						<Table
							rowKey="_id"
							size="small"
							scroll={{ x: "max-content", y: 800 }}
							pagination={
								pagination && pagination.totalCount > 0
									? {
											total: pagination.totalCount,
											current: pagination.currentPage,
											pageSize: getPageSize(),
											showSizeChanger: false,
											onChange: handlePageChange,
											position: ["bottomCenter"],
										}
									: false
							}
							columns={columns}
							dataSource={(brands || []).map((brand: Brand) => ({
								...brand,
								_id: brand._id || `temp-${Math.random()}`,
							}))}
							loading={isLoading || isFetching}
							locale={{
								emptyText: (
									<Empty
										image={Empty.PRESENTED_IMAGE_SIMPLE}
										description={searchName ? "没有找到匹配的品牌" : "暂无数据"}
									/>
								),
							}}
						/>
					</div>
				)}
			</CardContent>
			<BrandModal {...brandModalProps} />
		</Card>
	);
}
